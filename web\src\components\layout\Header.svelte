<script lang="ts">
  import Logo from '$components/ui/Logo.svelte';
  import { Button } from '$lib/components/ui/button';
  import {
    ArrowRightIcon,
    Zap,
    Target,
    FileText,
    Bot,
    Users,
    Building,
    Chrome,
    Newspaper,
    TrendingUp,
  } from 'lucide-svelte';
  import type { HTMLAttributes } from 'svelte/elements';
  import * as NavigationMenu from '$lib/components/ui/navigation-menu';
  import { navigationMenuTriggerStyle } from '$lib/components/ui/navigation-menu/navigation-menu-trigger.svelte';
  import { cn } from '$lib/utils';
  import { onDestroy, onMount } from 'svelte';
  import { graphqlRequest, COLLECTIONS_QUERY } from '$lib/graphql/client';
  import type { Collection } from '$lib/graphql/types';
  import { page } from '$app/stores';
  import { browser } from '$app/environment';
  import { goto } from '$app/navigation';

  const { currentUser = null, data = {} } = $props();

  let jobCollections = $state<Collection[]>([]);
  let topCollections = $state<Collection[]>([]);
  let isAtTop = true;

  // Products dropdown links with icons
  const productLinks = [
    {
      title: 'Auto Apply',
      href: '/auto-apply',
      description: 'Automatically apply to jobs that match your criteria',
      icon: Zap,
    },
    {
      title: 'Job Tracker',
      href: '/job-tracker',
      description: 'Track your job applications and interviews',
      icon: Target,
    },
    {
      title: 'Resume Builder',
      href: '/resume-builder',
      description: 'Create professional resumes with AI assistance',
      icon: FileText,
    },
    {
      title: 'AI Co-Pilot',
      href: '/co-pilot',
      description: 'Get personalized job search guidance',
      icon: Bot,
    },
    {
      title: 'Matches',
      href: '/matches',
      description: 'Get personalized automated job matches',
      icon: TrendingUp,
    },
    {
      title: 'Browser Extension',
      href: '/extension',
      description: 'Apply to jobs directly from job boards',
      icon: Chrome,
    },
  ];

  // News sections for the right side
  const newsItems = [
    {
      title: 'Resume is now compatible with Figma Sites',
      date: 'May 21, 2025',
      description: 'Build better resumes with our new Figma integration',
      image: '/news/figma-integration.jpg',
    },
    {
      title: 'Premium Monotype Fonts and Branded Sharing',
      date: 'April 9, 2025',
      description: 'Enhanced typography and sharing options',
      image: '/news/premium-fonts.jpg',
    },
  ];

  // Resources dropdown links
  const resourceLinks = [
    { title: 'Free Tools', href: '/resources' },
    {
      title: 'Resume Templates',
      href: '/resources/resume-templates',
    },
    {
      title: 'Cover Letter Templates',
      href: '/resources/cover-letters',
    },
    {
      title: 'ATS Resume Checker',
      href: '/resources/ats-optimization/checker',
    },
    {
      title: 'Interview Questions',
      href: '/resources/interview-prep/question-database',
    },
    {
      title: 'Salary Tools',
      href: '/resources/salary-tools',
    },
  ];

  // Load job collections
  async function loadJobCollections() {
    try {
      // Try to use collections from data prop first
      if (data?.jobCollections && Array.isArray(data.jobCollections)) {
        jobCollections = data.jobCollections;
      } else {
        // Fallback to GraphQL request
        const result = await graphqlRequest<{ collections: Collection[] }>(COLLECTIONS_QUERY);
        if (result.errors) {
          throw new Error(result.errors[0].message);
        }
        jobCollections = result.data?.collections || [];
      }

      // Get top 10 collections (alphabetically sorted)
      topCollections = jobCollections.slice(0, 10);
    } catch (error) {
      console.error('Error loading job collections for header:', error);
    }
  }

  // Initialize on mount
  onMount(() => {
    loadJobCollections();
  });

  // Function to determine if a nav item is active
  function isActive(href: string, exact = false) {
    if (!browser || !$page) return false;
    if (exact) {
      return $page.url.pathname === href;
    }
    return $page.url.pathname.includes(href);
  }

  // Scroll to top function
  function scrollToTop() {
    if (browser) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }

  // Handle launch button click
  function handleLaunchClick() {
    scrollToTop();
    // Small delay to allow scroll to start before navigation
    setTimeout(() => {
      goto('/dashboard');
    }, 100);
  }

  function onScroll() {
    isAtTop = window.scrollY === 0;
  }

  onMount(() => {
    onScroll(); // initial check in case the page loads scrolled down
    window.addEventListener('scroll', onScroll, { passive: true });
  });

  onDestroy(() => {
    window.removeEventListener('scroll', onScroll);
  });
</script>

<div class="top-6.5 fixed left-10 z-50 flex transform items-center [transform-style:preserve-3d]">
  <button
    class="rounded-xs bg-gradient-to-b from-purple-500 to-orange-400 p-0.5 transition-all"
    aria-label="Scroll to top"
    onclick={scrollToTop}>
    <Logo class="bg-secondary h-6 w-6" />
  </button>
</div>

<div class="animate-slide-up-3d fixed right-10 top-5 z-50 transform [transform-style:preserve-3d]">
  <div class="rounded-md bg-gradient-to-b from-purple-500 to-orange-400 p-0.5">
    <Button variant="default" size="lg" class="text-white" onclick={handleLaunchClick}>
      Launch <ArrowRightIcon class="h-4 w-4" />
    </Button>
  </div>
</div>

<header class="mt-2">
  <div class="mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <!-- Logo -->
      <span class="ml-12 text-xl font-bold text-gray-900">Hirli</span>

      <NavigationMenu.Root>
        <NavigationMenu.List>
          <!-- Products Megamenu -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Products</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <div class="grid w-[700px] grid-cols-2 gap-4 p-3">
                <!-- Left Column - Products with Icons -->
                <div>
                  <ul class="space-y-2">
                    {#each productLinks as product}
                      {@const Icon = product.icon}
                      <li>
                        <NavigationMenu.Link
                          href={product.href}
                          class={cn(
                            'flex flex-row items-center gap-2 rounded-lg',
                            isActive(product.href) ? 'bg-accent text-accent-foreground' : ''
                          )}>
                          <div
                            class="bg-primary/80 flex h-10 w-10 items-center justify-center rounded-md">
                            <Icon class="text-secondary h-5 w-5" />
                          </div>
                          <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900">{product.title}</div>
                            <p class="text-xs text-gray-500">{product.description}</p>
                          </div>
                        </NavigationMenu.Link>
                      </li>
                    {/each}
                  </ul>
                </div>

                <!-- Right Column - What's New -->
                <div class="bg-secondary rounded-md p-4">
                  <div class="mb-4 flex items-center justify-between">
                    <h3 class="text-sm font-medium text-gray-900">What's New</h3>
                    <a href="/blog" class="text-xs text-blue-600 hover:text-blue-700">View all</a>
                  </div>
                  <ul class="space-y-2">
                    {#each newsItems as news}
                      <li>
                        <NavigationMenu.Link
                          href="/news/{news.title.toLowerCase().replace(/\s+/g, '-')}"
                          class="flex flex-col gap-2 transition-colors">
                          <div
                            class="border-border h-20 w-full rounded-md border bg-gradient-to-br from-blue-100 to-purple-100">
                          </div>
                          <div class="flex flex-col gap-0">
                            <div class="text-primary mb-1 text-xs">{news.date}</div>
                            <div class=" font-lighter text-primary text-sm">{news.title}</div>
                          </div>
                        </NavigationMenu.Link>
                      </li>
                    {/each}
                  </ul>
                </div>
              </div>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Categories Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Sectors</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[200px] gap-1 p-2">
                {#each topCollections as collection}
                  <li>
                    <NavigationMenu.Link
                      href="/jobs?collection={collection.slug}"
                      class={cn(
                        'hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 text-sm leading-none no-underline outline-none transition-colors',
                        isActive('/jobs') &&
                          $page?.url.searchParams.get('collection') === collection.slug
                          ? 'bg-accent text-accent-foreground'
                          : ''
                      )}>
                      {collection.name}
                    </NavigationMenu.Link>
                  </li>
                {/each}
                <li>
                  <NavigationMenu.Link
                    href="/jobs"
                    class={cn(
                      'hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 text-sm leading-none no-underline outline-none transition-colors',
                      isActive('/jobs', true) ? 'bg-accent text-accent-foreground' : ''
                    )}>
                    Browse All Jobs
                  </NavigationMenu.Link>
                </li>
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Resources Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Resources</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[200px] gap-1 p-2">
                {#each resourceLinks as resource}
                  <li>
                    <NavigationMenu.Link
                      href={resource.href}
                      class={cn(
                        'hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 text-sm leading-none no-underline outline-none transition-colors',
                        isActive(resource.href) ? 'bg-accent text-accent-foreground' : ''
                      )}>
                      {resource.title}
                    </NavigationMenu.Link>
                  </li>
                {/each}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link
              href="/pricing"
              class={cn(
                navigationMenuTriggerStyle(),
                isActive('/pricing') ? 'bg-accent text-accent-foreground' : ''
              )}>
              Pricing
            </NavigationMenu.Link>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link
              href="/recruiters"
              class={cn(
                navigationMenuTriggerStyle(),
                isActive('/recruiters') ? 'bg-accent text-accent-foreground' : ''
              )}>
              Recruiters
            </NavigationMenu.Link>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link
              href="/employers"
              class={cn(
                navigationMenuTriggerStyle(),
                isActive('/employers') ? 'bg-accent text-accent-foreground' : ''
              )}>
              Employers
            </NavigationMenu.Link>
          </NavigationMenu.Item>
        </NavigationMenu.List>
      </NavigationMenu.Root>

      <!-- Right Actions -->
      <div></div>
    </div>
  </div>
</header>
